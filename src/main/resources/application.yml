app:
  id: customer-web
apollo:
  bootstrap:
    enabled: true
    namespaces: global.properties,application.properties
logging:
  config: ~
  level:
    root: INFO
    com.github.even.ki4so: DEBUG
    org.springframework.web: ERROR
    com.tem: DEBUG
    org.hibernate: ERROR
    com.platform: ERROR
server:
  port: 8099
  servlet:
    multipart:
      max-file-size: 128MB
      max-request-size: 128MB
spring:
  application:
    name: customer-web
  session:
    store-type: redis
  data:
    redis:
      sentinel:
        master: ${cluster.sentinel.masterName}
        nodes: ${cluster.address}
      password: ${cluster.password}
      jedis:
        pool:
          max-active: 300
          max-idle: 8
          min-idle: 8
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: Asia/Shanghai
    default-property-inclusion: always
dubbo:
  application:
    name: customer-web
    owner: tem
    organization: tem
    qos-enable: true
    qos-port: 33333
    qos-anonymous-access-permission-level: NONE
  registry:
    protocol: zookeeper
    address: ${zookeeper.cluster}
    client: curator
    file: ./.dubbo-workflow-web
    timeout: 60000
    parameters:
      blockUntilConnectedWait: 30
      init: 60000
      timeout: 60000
  config-center:
    timeout: 20000
  consumer:
    check: false
    timeout: 20000
    #group:
    provided-by: tem-service
  provider:
    timeout: 20000
    #token: false
    serialization: hessian2
    #retries: 0
    #actives: 30
    #accepts: 1000
    #register: false
  protocol:
    id: dubbo
    name: dubbo
    payload: 83886080
    port: 21886
    serialization: hessian2
    #dispatcher: all
    #threadpool: fixed
    #threads: 1000
